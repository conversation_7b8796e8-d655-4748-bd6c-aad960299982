import { InspectionFormTypeEnum } from "@/modules/inspection/constants/form/type-enum";
import { ICreateForm, IFieldTable } from "@/modules/inspection/validators/form/create";
import { Button } from "@/shared/components/shadcn/button";
import { Card, CardContent } from "@/shared/components/shadcn/card";
import { Checkbox } from "@/shared/components/shadcn/checkbox";
import { FormControl, FormField, FormItem, FormLabel } from "@/shared/components/shadcn/form";
import { Input } from "@/shared/components/shadcn/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/shared/components/shadcn/select";
import { ChevronUp, ChevronDown, Trash2 } from "lucide-react";
import { Control } from "react-hook-form";
import { FieldOptions } from "./field-options";
import { useState } from "react";

interface MobileFieldCardProps {
	field: IFieldTable;
	index: number;
	control: Control<ICreateForm>;
	onDelete: (index: number) => void;
	onMoveUp: (index: number) => void;
	onMoveDown: (index: number) => void;
	onCardClick?: (tempId: string) => void;
	isSelected: boolean;
	isFirst: boolean;
	isLast: boolean;
	isMoving?: boolean;
}

export const MobileFieldCard: React.FC<MobileFieldCardProps> = ({
	field,
	index,
	control,
	onDelete,
	onMoveUp,
	onMoveDown,
	onCardClick,
	isSelected,
	isFirst,
	isLast,
	isMoving = false,
}) => {
	const [isAnimating, setIsAnimating] = useState(false);
	const [animationType, setAnimationType] = useState<"up" | "down" | null>(null);

	const handleCardClick = () => onCardClick?.(field.tempId || `field-${index}`);

	const handleMove = (type: "up" | "down", fn: (i: number) => void) => (e: React.MouseEvent) => {
		e.stopPropagation();
		setIsAnimating(true);
		setAnimationType(type);
		setTimeout(() => {
			fn(index);
			setTimeout(() => {
				setIsAnimating(false);
				setAnimationType(null);
			}, 100);
		}, 150);
	};

	const getAnimationClasses = () => (isAnimating ? (animationType === "up" ? "animate-field-move-up" : "animate-field-move-down") : "");

	return (
		<div
			className={`transition-all duration-300 ease-in-out ${isAnimating ? "scale-98 opacity-90" : "scale-100 opacity-100"} ${isMoving ? "animate-field-reorder z-10" : "z-0"} ${getAnimationClasses()}`}
		>
			<Card
				className={`mb-3 cursor-pointer transition-all duration-200 ease-in-out ${isSelected ? "ring-primary bg-muted/20 ring-2" : ""} ${isAnimating ? "shadow-lg" : ""}`}
				onClick={handleCardClick}
			>
				<CardContent className="p-4">
					<div className="mb-4 flex items-center justify-between">
						<div className="flex gap-2">
							<Button
								variant="outline"
								size="sm"
								onClick={handleMove("up", onMoveUp)}
								disabled={isFirst || isAnimating}
								className={`flex h-8 items-center gap-1 rounded-full px-2 py-1 shadow-sm transition-all duration-200 ${isAnimating && animationType === "up" ? "scale-105 bg-blue-50 text-blue-600" : "hover:bg-muted"} ${isAnimating ? "opacity-80" : "opacity-100"}`}
							>
								<ChevronUp
									className={`h-4 w-4 transition-transform duration-200 ${isAnimating && animationType === "up" ? "-translate-y-1 transform" : ""}`}
								/>
								<span className="text-xs font-medium">Mover</span>
							</Button>
							<Button
								variant="outline"
								size="sm"
								onClick={handleMove("down", onMoveDown)}
								disabled={isLast || isAnimating}
								className={`flex h-8 items-center gap-1 rounded-full px-2 py-1 shadow-sm transition-all duration-200 ${isAnimating && animationType === "down" ? "scale-105 bg-blue-50 text-blue-600" : "hover:bg-muted"} ${isAnimating ? "opacity-80" : "opacity-100"}`}
							>
								<ChevronDown
									className={`h-4 w-4 transition-transform duration-200 ${isAnimating && animationType === "down" ? "translate-y-1 transform" : ""}`}
								/>
								<span className="text-xs font-medium">Mover</span>
							</Button>
						</div>
						<Button
							variant="ghost"
							size="sm"
							onClick={e => {
								e.stopPropagation();
								onDelete(index);
							}}
							className="text-destructive hover:text-destructive h-8 w-8 p-0"
						>
							<Trash2 className="h-4 w-4" />
							<span className="sr-only">Excluir campo</span>
						</Button>
					</div>

					<div className="space-y-4">
						<FormField
							control={control}
							name={`fields.${index}.nickname`}
							render={({ field }) => (
								<FormItem>
									<FormLabel className="text-sm font-medium">
										Nome do Campo <span className="text-red-500">*</span>
									</FormLabel>
									<FormControl>
										<Input
											placeholder="Nome do campo"
											{...field}
											value={field.value || ""}
											onClick={e => e.stopPropagation()}
											key={`nickname-mobile-${field.tempId}`}
										/>
									</FormControl>
								</FormItem>
							)}
						/>

						<div className="grid grid-cols-2 gap-3">
							<FormField
								control={control}
								name={`fields.${index}.typeId`}
								render={({ field }) => (
									<FormItem>
										<FormLabel className="text-sm font-medium">
											Tipo <span className="text-red-500">*</span>
										</FormLabel>
										<FormControl className="w-full">
											<Select
												value={field.value?.toString()}
												onValueChange={value => field.onChange(Number(value) as IFieldTable["typeId"])}
											>
												<SelectTrigger className="w-full">
													<SelectValue placeholder="Tipo" />
												</SelectTrigger>
												<SelectContent align="end">
													{Object.entries(InspectionFormTypeEnum).map(([key, val]) => (
														<SelectItem key={val} value={val.toString()}>
															{key.charAt(0) + key.slice(1).toLowerCase()}
														</SelectItem>
													))}
												</SelectContent>
											</Select>
										</FormControl>
									</FormItem>
								)}
							/>
							<FormField
								control={control}
								name={`fields.${index}.measureId`}
								render={({ field }) => (
									<FormItem>
										<FormLabel className="text-sm font-medium">
											Medida <span className="text-red-500">*</span>
										</FormLabel>
										<FormControl>
											<Select
												value={field.value?.toString()}
												onValueChange={value => field.onChange(Number(value) as IFieldTable["measureId"])}
											>
												<SelectTrigger className="w-full">
													<SelectValue placeholder="Medida" />
												</SelectTrigger>
												<SelectContent align="end">
													{[1, 2, 3, 4].map(m => (
														<SelectItem key={m} value={m.toString()}>
															{`Medida ${m}`}
														</SelectItem>
													))}
												</SelectContent>
											</Select>
										</FormControl>
									</FormItem>
								)}
							/>
						</div>
						<div className="grid grid-cols-2 gap-3">
							<FormField
								control={control}
								name={`fields.${index}.group`}
								render={({ field }) => (
									<FormItem>
										<FormLabel className="text-sm font-medium">
											Grupo <span className="text-red-500">*</span>
										</FormLabel>
										<FormControl>
											<Input
												placeholder="Grupo"
												type="number"
												{...field}
												value={field.value?.toString() || ""}
												onChange={e => field.onChange(Number(e.target.value) || 1)}
												onClick={e => e.stopPropagation()}
											/>
										</FormControl>
									</FormItem>
								)}
							/>
							<FormField
								control={control}
								name={`fields.${index}.groupTitle`}
								render={({ field }) => (
									<FormItem>
										<FormLabel className="text-sm font-medium">
											Título do Grupo <span className="text-red-500">*</span>
										</FormLabel>
										<FormControl>
											<Input placeholder="Título" {...field} value={field.value || ""} onClick={e => e.stopPropagation()} />
										</FormControl>
									</FormItem>
								)}
							/>
						</div>
						<div className="flex gap-6">
							<FormField
								control={control}
								name={`fields.${index}.required`}
								render={({ field }) => (
									<FormItem className="flex items-center space-y-0 space-x-2">
										<FormControl>
											<Checkbox
												checked={field.value || false}
												onCheckedChange={value => field.onChange(!!value)}
												onClick={e => e.stopPropagation()}
											/>
										</FormControl>
										<FormLabel className="cursor-pointer text-sm font-medium">Obrigatório</FormLabel>
									</FormItem>
								)}
							/>
							<FormField
								control={control}
								name={`fields.${index}.biFilter`}
								render={({ field }) => (
									<FormItem className="flex items-center space-y-0 space-x-2">
										<FormControl>
											<Checkbox
												checked={field.value || false}
												onCheckedChange={value => field.onChange(!!value)}
												onClick={e => e.stopPropagation()}
											/>
										</FormControl>
										<FormLabel className="cursor-pointer text-sm font-medium">Filtro BI</FormLabel>
									</FormItem>
								)}
							/>
						</div>
						{field.typeId === InspectionFormTypeEnum.OPTIONS && (
							<div className="mt-4 border-t pt-4">
								<FieldOptions fieldIndex={index} control={control} field={field} />
							</div>
						)}
					</div>
				</CardContent>
			</Card>
		</div>
	);
};
