import { InspectionFormTypeEnum } from "@/modules/inspection/constants/form/type-enum";
import { ICreateForm, IFieldTable } from "@/modules/inspection/validators/form/create";
import { Button } from "@/shared/components/shadcn/button";
import { FormControl, FormField, FormItem, FormMessage } from "@/shared/components/shadcn/form";
import { Input } from "@/shared/components/shadcn/input";
import { Plus, Trash2 } from "lucide-react";
import { Control, useFieldArray } from "react-hook-form";

interface FieldOptionsProps {
	fieldIndex: number;
	control: Control<ICreateForm>;
	field: IFieldTable;
}

export const FieldOptions: React.FC<FieldOptionsProps> = ({ fieldIndex, control, field }) => {
	const {
		fields: optionFields,
		append,
		remove,
	} = useFieldArray({
		control,
		name: `fields.${fieldIndex}.options`,
	});

	if (field.typeId !== InspectionFormTypeEnum.OPTIONS) {
		return null;
	}

	return (
		<div className="border-muted space-y-4 border-b p-2">
			<div className="flex items-center justify-between">
				<h4 className="text-sm font-medium">Opções para {field.nickname || `Campo ${fieldIndex + 1}`}</h4>
				<Button type="button" variant="outline" size="sm" onClick={() => append({ sequence: optionFields.length + 1, option: "" })}>
					<Plus className="mr-1 h-4 w-4" />
					Adicionar Opção
				</Button>
			</div>
			{optionFields.length === 0 ? (
				<p className="text-muted-foreground text-sm">Nenhuma opção adicionada. Clique em &quot;Adicionar Opção&quot; para começar.</p>
			) : (
				<div className="space-y-2">
					{optionFields.map((option, optionIndex) => (
						<div key={option.id} className="flex items-start gap-2">
							<div className="flex-1">
								<FormField
									control={control}
									name={`fields.${fieldIndex}.options.${optionIndex}.option`}
									render={({ field }) => (
										<FormItem>
											<FormControl>
												<Input placeholder={`Opção ${optionIndex + 1}`} {...field} value={field.value || ""} />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>
							<Button
								type="button"
								variant="ghost"
								size="sm"
								onClick={() => remove(optionIndex)}
								className="text-destructive hover:text-destructive h-9 w-9 p-0"
							>
								<Trash2 className="h-4 w-4" />
								<span className="sr-only">Remover opção</span>
							</Button>
						</div>
					))}
				</div>
			)}
		</div>
	);
};
