import { InspectionFormTypeEnum } from "@/modules/inspection/constants/form/type-enum";
import { ICreateForm, IFieldTable } from "@/modules/inspection/validators/form/create";
import { Button } from "@/shared/components/shadcn/button";
import { Checkbox } from "@/shared/components/shadcn/checkbox";
import { FormControl, FormField, FormItem } from "@/shared/components/shadcn/form";
import { Input } from "@/shared/components/shadcn/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/shared/components/shadcn/select";
import { ColumnDef } from "@tanstack/react-table";
import { Trash2 } from "lucide-react";
import { Control } from "react-hook-form";

export const createColumnsFieldForm = (options?: {
	onDelete?: (index: number) => void;
	onEdit?: (index: number, field: IFieldTable) => void;
	control: Control<ICreateForm>;
}): ColumnDef<IFieldTable>[] => [
	{
		id: "drag-handle",
		header: () => null,
		cell: () => null,
		size: 48,
		enableSorting: false,
		enableHiding: false,
	},

	{
		accessorKey: "nickname",
		header: () => (
			<div className="flex items-center gap-2">
				Nome do Campo
				<span className="text-red-500">*</span>
			</div>
		),
		size: 200,
		cell: ({ row }) => (
			<FormField
				control={options?.control}
				name={`fields.${row.index}.nickname`}
				render={({ field }) => (
					<FormItem>
						<FormControl>
							<Input placeholder="Nome do campo" {...field} value={field.value || ""} key={`nickname-${row.original.tempId || row.id}`} />
						</FormControl>
					</FormItem>
				)}
			/>
		),
	},
	{
		accessorKey: "typeId",
		header: () => (
			<div className="flex items-center gap-2">
				Tipo de Campo
				<span className="text-red-500">*</span>
			</div>
		),
		size: 120,
		cell: ({ row }) => (
			<FormField
				control={options?.control}
				name={`fields.${row.index}.typeId`}
				render={({ field }) => (
					<FormItem>
						<FormControl>
							<Select
								value={field.value?.toString()}
								onValueChange={value => {
									const typeId = Number(value) as IFieldTable["typeId"];
									field.onChange(typeId);
								}}
								key={`typeId-${row.original.tempId || row.id}`}
							>
								<SelectTrigger className="w-38 **:data-[slot=select-value]:block **:data-[slot=select-value]:truncate">
									<SelectValue placeholder="Tipo de campo" />
								</SelectTrigger>
								<SelectContent align="end">
									<SelectItem value={InspectionFormTypeEnum.DATE.toString()}>Data</SelectItem>
									<SelectItem value={InspectionFormTypeEnum.TEXT.toString()}>Texto</SelectItem>
									<SelectItem value={InspectionFormTypeEnum.OPTIONS.toString()}>Opções</SelectItem>
									<SelectItem value={InspectionFormTypeEnum.NUMBER.toString()}>Número</SelectItem>
									<SelectItem value={InspectionFormTypeEnum.BOOLEAN.toString()}>Booleano</SelectItem>
								</SelectContent>
							</Select>
						</FormControl>
					</FormItem>
				)}
			/>
		),
	},
	{
		accessorKey: "measureId",
		header: () => (
			<div className="flex items-center gap-2">
				Medida
				<span className="text-red-500">*</span>
			</div>
		),
		size: 80,
		cell: ({ row }) => (
			<FormField
				control={options?.control}
				name={`fields.${row.index}.measureId`}
				render={({ field }) => (
					<FormItem>
						<FormControl>
							<Select
								value={field.value?.toString()}
								onValueChange={value => {
									const measureId = Number(value) as IFieldTable["measureId"];
									field.onChange(measureId);
								}}
								key={`measureId-${row.original.tempId || row.id}`}
							>
								<SelectTrigger className="w-38 **:data-[slot=select-value]:block **:data-[slot=select-value]:truncate">
									<SelectValue placeholder="Medida" />
								</SelectTrigger>
								<SelectContent align="end">
									<SelectItem value="1">Medida 1</SelectItem>
									<SelectItem value="2">Medida 2</SelectItem>
									<SelectItem value="3">Medida 3</SelectItem>
									<SelectItem value="4">Medida 4</SelectItem>
								</SelectContent>
							</Select>
						</FormControl>
					</FormItem>
				)}
			/>
		),
	},
	{
		accessorKey: "group",
		header: () => (
			<div className="flex items-center gap-2">
				Grupo
				<span className="text-red-500">*</span>
			</div>
		),
		size: 80,
		cell: ({ row }) => (
			<FormField
				control={options?.control}
				name={`fields.${row.index}.group`}
				render={({ field }) => (
					<FormItem>
						<FormControl>
							<Input
								placeholder="Grupo"
								type="number"
								{...field}
								value={field.value?.toString() || ""}
								onChange={e => {
									const value = Number(e.target.value) || 1;
									field.onChange(value);
								}}
								key={`group-${row.original.tempId || row.id}`}
							/>
						</FormControl>
					</FormItem>
				)}
			/>
		),
	},
	{
		accessorKey: "groupTitle",
		header: () => (
			<div className="flex items-center gap-2">
				Título do Grupo
				<span className="text-red-500">*</span>
			</div>
		),
		size: 150,
		cell: ({ row }) => (
			<FormField
				control={options?.control}
				name={`fields.${row.index}.groupTitle`}
				render={({ field }) => (
					<FormItem>
						<FormControl>
							<Input placeholder="Título do grupo" {...field} value={field.value || ""} key={`groupTitle-${row.original.tempId || row.id}`} />
						</FormControl>
					</FormItem>
				)}
			/>
		),
	},
	{
		accessorKey: "required",
		header: "Obrigatório",
		size: 100,
		cell: ({ row }) => (
			<FormField
				control={options?.control}
				name={`fields.${row.index}.required`}
				render={({ field }) => (
					<FormItem className="flex items-center justify-center">
						<FormControl className="flex items-center">
							<Checkbox
								checked={field.value || false}
								onCheckedChange={value => {
									const isRequired = !!value;
									field.onChange(isRequired);
								}}
								key={`required-${row.original.tempId || row.id}`}
							/>
						</FormControl>
					</FormItem>
				)}
			/>
		),
	},

	{
		accessorKey: "biFilter",
		header: "Filtro BI",
		size: 90,
		cell: ({ row }) => (
			<FormField
				control={options?.control}
				name={`fields.${row.index}.biFilter`}
				render={({ field }) => (
					<FormItem className="flex items-center justify-center">
						<FormControl>
							<Checkbox
								checked={field.value || false}
								onCheckedChange={value => {
									const isBiFilter = !!value;
									field.onChange(isBiFilter);
								}}
								key={`biFilter-${row.original.tempId || row.id}`}
							/>
						</FormControl>
					</FormItem>
				)}
			/>
		),
	},
	{
		id: "actions",
		header: "Ações",
		size: 100,
		cell: ({ row }) => (
			<div className="flex items-center justify-center gap-1">
				{options?.onDelete && (
					<Button
						variant="ghost"
						size="sm"
						onClick={e => {
							e.stopPropagation();
							options.onDelete?.(row.index);
						}}
						className="text-destructive hover:text-destructive h-8 w-8 p-0"
					>
						<Trash2 className="h-4 w-4" />
						<span className="sr-only">Excluir campo</span>
					</Button>
				)}
			</div>
		),
		enableSorting: false,
		enableHiding: false,
	},
];
