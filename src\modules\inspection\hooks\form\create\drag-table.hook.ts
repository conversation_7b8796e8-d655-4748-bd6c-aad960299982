import { DragEndEvent, KeyboardSensor, MouseSensor, TouchSensor, UniqueIdentifier, useSensor, useSensors } from "@dnd-kit/core";
import { useCallback, useId, useMemo, useState } from "react";
import { IFieldTable } from "../../../validators/form/create";

interface UseCreateFormDragTableProps {
	fields: IFieldTable[];
	reorderFields: (oldIndex: number, newIndex: number) => void;
}

export const useCreateFormDragTable = ({ fields, reorderFields }: UseCreateFormDragTableProps) => {
	const sortableId = useId();
	const [activeId, setActiveId] = useState<UniqueIdentifier | null>(null);

	// Use os tempIds únicos dos campos em vez de índices
	const dataIds = useMemo(() => {
		return fields.map((field, index) => field.tempId || `field-${index}`);
	}, [fields]);

	const sensors = useSensors(
		useSensor(MouseSensor, {
			activationConstraint: {
				distance: 8,
			},
		}),
		useSensor(TouchSensor, {
			activationConstraint: {
				delay: 200,
				tolerance: 5,
			},
		}),
		useSensor(KeyboardSensor, {}),
	);

	const handleDragStart = useCallback((event: { active: { id: UniqueIdentifier } }) => {
		setActiveId(event.active.id);
	}, []);

	const handleDragEnd = useCallback(
		(event: DragEndEvent) => {
			const { active, over } = event;
			setActiveId(null);

			if (active && over && active.id !== over.id) {
				const activeId = active.id as string;
				const overId = over.id as string;

				// Encontre os índices baseados nos tempIds únicos
				const oldIndex = fields.findIndex(field => (field.tempId && field.tempId === activeId) || `field-${fields.indexOf(field)}` === activeId);

				const newIndex = fields.findIndex(field => (field.tempId && field.tempId === overId) || `field-${fields.indexOf(field)}` === overId);

				if (oldIndex !== newIndex && oldIndex >= 0 && newIndex >= 0 && oldIndex < fields.length && newIndex < fields.length) {
					reorderFields(oldIndex, newIndex);
				}
			}
		},
		[fields, reorderFields],
	);

	const handleDragCancel = useCallback(() => {
		setActiveId(null);
	}, []);

	return {
		sortableId,
		sensors,
		dataIds,
		activeId,
		handleDragStart,
		handleDragEnd,
		handleDragCancel,
	};
};
