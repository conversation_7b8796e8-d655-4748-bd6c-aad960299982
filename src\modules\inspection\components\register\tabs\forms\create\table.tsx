"use client";
import { useCreateFormDragTable } from "@/modules/inspection/hooks/form/create/drag-table.hook";
// import { restrictToTableAreaVertical } from "@/modules/inspection/hooks/form/create/modifiers";
import { ICreateForm, IFieldTable } from "@/modules/inspection/validators/form/create";
import { Button } from "@/shared/components/shadcn/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/shared/components/shadcn/table";
import { DndContext, closestCenter } from "@dnd-kit/core";
import { restrictToParentElement } from "@dnd-kit/modifiers";
import { SortableContext, verticalListSortingStrategy } from "@dnd-kit/sortable";
import { flexRender, getCoreRowModel, useReactTable } from "@tanstack/react-table";
import { Plus } from "lucide-react";
import { useMemo, useState } from "react";
import { Control } from "react-hook-form";
import { createColumnsFieldForm } from "./columns";
import { SortableRow } from "./sortable-row";
import { MobileFieldCard } from "./mobile-field-card";

interface TableCreateFormFieldsProps {
	fields: IFieldTable[];
	control: Control<ICreateForm>;
	addField: (field: Omit<IFieldTable, "sequence">) => void;
	removeField: (index: number) => void;
	updateField: (index: number, field: Partial<IFieldTable>) => void;
	reorderFields: (oldIndex: number, newIndex: number) => void;
	isFieldTypeOptions: (typeId: number) => boolean;
}

export const TableCreateFormFields: React.FC<TableCreateFormFieldsProps> = ({ fields, addField, removeField, reorderFields, isFieldTypeOptions, control }) => {
	const [selectedFieldId, setSelectedFieldId] = useState<string | null>(null);
	const [movingFieldId, setMovingFieldId] = useState<string | null>(null);
	const { sortableId, sensors, dataIds, handleDragStart, handleDragEnd, handleDragCancel } = useCreateFormDragTable({
		fields,
		reorderFields,
	});

	const columns = useMemo(
		() =>
			createColumnsFieldForm({
				control,
				onDelete: (index: number) => removeField(index),
			}),
		[control, removeField],
	);

	const table = useReactTable({
		data: fields,
		columns,
		getCoreRowModel: getCoreRowModel(),
		getRowId: (row, index) => row.tempId || `field-${index}`,
	});

	const generateFieldId = () => `field-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

	return (
		<div className="mt-2 flex flex-col gap-4">
			<div className="hidden w-full justify-end lg:flex">
				<Button
					variant="outline"
					onClick={e => {
						e.preventDefault();
						addField({
							tempId: generateFieldId(),
							fieldId: undefined,
							groupTitle: "",
							nickname: "",
							required: false,
							group: 1,
							typeId: 1,
							measureId: 1,
							biFilter: false,
						});
					}}
				>
					<Plus className="ml-2 size-4" />
					Adicionar campo
				</Button>
			</div>

			<div className="hidden max-h-[500px] w-full overflow-auto rounded-lg border lg:block" data-dnd-table-container>
				<DndContext
					collisionDetection={closestCenter}
					modifiers={[restrictToParentElement]}
					onDragStart={handleDragStart}
					onDragEnd={handleDragEnd}
					onDragCancel={handleDragCancel}
					sensors={sensors}
					id={sortableId}
				>
					<Table className="w-full flex-1" data-dnd-table>
						<TableHeader className="bg-muted sticky top-0 z-10">
							{table.getHeaderGroups().map(headerGroup => (
								<TableRow key={headerGroup.id}>
									{headerGroup.headers.map(header => (
										<TableHead key={header.id} colSpan={header.colSpan}>
											{header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
										</TableHead>
									))}
								</TableRow>
							))}
						</TableHeader>

						<TableBody>
							{table.getRowModel().rows?.length ? (
								<SortableContext items={dataIds} strategy={verticalListSortingStrategy}>
									{table.getRowModel().rows.map(row => (
										<SortableRow
											key={row.id}
											row={row}
											onRowClick={setSelectedFieldId}
											isSelected={selectedFieldId === row.original.tempId || selectedFieldId === `field-${row.index}`}
											control={control}
											isFieldTypeOptions={isFieldTypeOptions}
										/>
									))}
								</SortableContext>
							) : (
								<TableRow>
									<TableCell colSpan={table.getAllLeafColumns().length} className="h-32 text-center">
										<div className="text-muted-foreground flex flex-col items-center gap-2">
											<Plus className="h-8 w-8 opacity-50" />
											<div>
												<p className="font-medium">Nenhum campo adicionado</p>
												<p className="text-sm">{`Clique em "Adicionar campo" para começar`}</p>
											</div>
										</div>
									</TableCell>
								</TableRow>
							)}
						</TableBody>
					</Table>
				</DndContext>
			</div>
			<div className="flex flex-col gap-3 overflow-auto lg:hidden">
				{fields.length > 0 ? (
					<div className="space-y-2">
						{fields.map((field, index) => (
							<div key={field.tempId || `field-${index}`} className="transition-all duration-300 ease-in-out">
								<MobileFieldCard
									field={field}
									index={index}
									control={control}
									onDelete={removeField}
									onMoveUp={index => {
										if (index > 0) {
											const fieldId = field.tempId || `field-${index}`;
											setMovingFieldId(fieldId);
											setTimeout(() => {
												reorderFields(index, index - 1);
												setMovingFieldId(null);
											}, 150);
										}
									}}
									onMoveDown={index => {
										if (index < fields.length - 1) {
											const fieldId = field.tempId || `field-${index}`;
											setMovingFieldId(fieldId);
											setTimeout(() => {
												reorderFields(index, index + 1);
												setMovingFieldId(null);
											}, 150);
										}
									}}
									onCardClick={setSelectedFieldId}
									isSelected={selectedFieldId === field.tempId || selectedFieldId === `field-${index}`}
									isFirst={index === 0}
									isLast={index === fields.length - 1}
									isMoving={movingFieldId === (field.tempId || `field-${index}`)}
								/>
							</div>
						))}
					</div>
				) : (
					<div className="text-muted-foreground rounded-controls flex h-32 flex-col items-center justify-center border text-center">
						<Plus className="mb-2 h-8 w-8 opacity-50" />
						<p className="font-medium">Nenhum campo adicionado</p>
						<p className="text-sm">{`Clique em "Adicionar campo" para começar`}</p>
					</div>
				)}

				<div className="flex w-full items-center justify-center">
					<Button
						variant="outline"
						onClick={e => {
							e.preventDefault();
							addField({
								tempId: generateFieldId(),
								fieldId: undefined,
								groupTitle: "",
								nickname: "",
								required: false,
								group: 1,
								typeId: 1,
								measureId: 1,
								biFilter: false,
							});
						}}
					>
						<Plus className="ml-2 size-4" />
						Adicionar campo
					</Button>
				</div>
			</div>
		</div>
	);
};
