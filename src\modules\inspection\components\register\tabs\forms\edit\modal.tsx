import { useCreateForm } from "@/modules/inspection/hooks/form/create/create-form.hook";
import { useUpdateFormMutation } from "@/modules/inspection/hooks/form/edit/update-form-mutation.hook";
import { useFormFindById } from "@/modules/inspection/hooks/form/list/find-by-id.hook";
import { InspectionFormFindByIdToFormMapper } from "@/modules/inspection/lib/mappers/find-by-id-to-form.mapper";
import { InspectionFormToUpdateMapper } from "@/modules/inspection/lib/mappers/form-to-update.mapper";
import { ICreateForm } from "@/modules/inspection/validators/form/create";
import { Modal } from "@/shared/components/custom/modal";
import { useEffect } from "react";
import { FormCreateForm } from "../create/form";

interface IModalEditFormProps {
	isOpen: boolean;
	onClose: () => void;
	formId: string;
}

export const ModalEditForm: React.FC<IModalEditFormProps> = ({ isOpen, onClose, formId }) => {
	const { methods, addField, removeField, update<PERSON>ield, reorder<PERSON>ields, isFieldTypeOptions } = useCreateForm();
	const { updateForm } = useUpdateFormMutation();
	const { data, isLoading, hasError, error } = useFormFindById(formId, isOpen);

	useEffect(() => {
		if (data) methods.reset(InspectionFormFindByIdToFormMapper.map(data));
	}, [data, isOpen, methods]);

	const handleSubmit = (formData: ICreateForm) => {
		updateForm({ form: InspectionFormToUpdateMapper.map(formData), id: formId });
		onClose();
	};

	return (
		<Modal
			isOpen={isOpen}
			onClose={onClose}
			className="!h-[95vh] !w-full !max-w-[95vw] md:!h-auto md:!w-[1400px] md:!max-w-none"
			title="Edição de Formulário"
		>
			{isLoading && <div>Carregando...</div>}
			{hasError && <div className="text-red-500">{error}</div>}
			{data && (
				<FormCreateForm
					mode="edit"
					onClose={onClose}
					methods={methods}
					onSubmit={handleSubmit}
					addField={addField}
					removeField={removeField}
					updateField={updateField}
					reorderFields={reorderFields}
					isFieldTypeOptions={isFieldTypeOptions}
				/>
			)}
		</Modal>
	);
};
