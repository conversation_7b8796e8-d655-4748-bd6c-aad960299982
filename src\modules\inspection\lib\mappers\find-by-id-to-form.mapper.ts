import { IFormFindByIdDto } from "../../types/forms/find-by-id.dto";
import { ICreateForm } from "../../validators/form/create";

export class InspectionFormFindByIdToFormMapper {
	static map(data: IFormFindByIdDto): ICreateForm {
		return {
			title: data.title,
			text: data.text,
			nomenclature: data.nomenclature,
			developerId: data.developer.id.toString(),
			approverId: data.approver.id.toString(),
			fields: data.fields.map(field => ({
				id: field.id.toString() ?? undefined,
				tempId: `field-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
				fieldId: field?.field?.id,
				nickname: field?.nickname,
				required: field?.required,
				group: field?.group,
				sequence: field?.sequence,
				typeId: field?.fieldType?.id,
				measureId: field?.measure?.id,
				groupTitle: field?.groupTitle,
				biFilter: field?.biFilter,
				options: (field?.options ?? []).map(opt => ({
					sequence: opt?.sequence,
					option: opt?.option,
					id: opt?.id,
				})),
			})),
		};
	}
}
