import { InspectionFormTypeEnum } from "@/modules/inspection/constants/form/type-enum";
import { createFormSchema, ICreateForm, IFieldTable } from "@/modules/inspection/validators/form/create";
import { zodResolver } from "@hookform/resolvers/zod";
import { useCallback } from "react";
import { useForm, UseFormReturn } from "react-hook-form";

interface UseCreateFormReturn {
	methods: UseFormReturn<ICreateForm>;
	addField: (field: Omit<IFieldTable, "sequence">) => void;
	removeField: (index: number) => void;
	updateField: (index: number, field: Partial<IFieldTable>) => void;
	reorderFields: (oldIndex: number, newIndex: number) => void;
	resetForm: () => void;
	isFieldTypeOptions: (typeId: number) => boolean;
}

export const useCreateForm = (): UseCreateFormReturn => {
	const methods = useForm<ICreateForm>({
		resolver: zodResolver(createFormSchema),
		defaultValues: {
			title: "",
			text: "",
			nomenclature: "",
			developerId: "",
			approverId: "",
			fields: [],
		},
		mode: "onChange",
	});

	const { getValues, setValue } = methods;

	const addField = useCallback(
		(field: Omit<IFieldTable, "sequence">) => {
			const currentFields = getValues("fields");
			const newField: IFieldTable = {
				...field,
				sequence: currentFields.length + 1,
			};
			setValue("fields", [...currentFields, newField], { shouldValidate: true });
		},
		[getValues, setValue],
	);

	const removeField = useCallback(
		(index: number) => {
			const currentFields = getValues("fields");
			const updatedFields = currentFields.filter((_, i) => i !== index);
			const reorderedFields = updatedFields.map((field, i) => ({
				...field,
				sequence: i + 1,
			}));
			setValue("fields", reorderedFields, { shouldValidate: true });
		},
		[getValues, setValue],
	);

	const updateField = useCallback(
		(index: number, updatedField: Partial<IFieldTable>) => {
			const currentFields = getValues("fields");
			const newFields = currentFields.map((field, i) => (i === index ? { ...field, ...updatedField } : field));
			setValue("fields", newFields, { shouldValidate: true });
		},
		[getValues, setValue],
	);

	const reorderFields = useCallback(
		(oldIndex: number, newIndex: number) => {
			const currentFields = getValues("fields");
			if (oldIndex < 0 || newIndex < 0 || oldIndex >= currentFields.length || newIndex >= currentFields.length) return;
			const reorderedFields = [...currentFields];
			const [movedField] = reorderedFields.splice(oldIndex, 1);
			reorderedFields.splice(newIndex, 0, movedField);
			const updatedFields = reorderedFields.map((field, index) => ({
				...field,
				sequence: index + 1,
			}));

			setValue("fields", updatedFields, { shouldValidate: true, shouldDirty: true });
			// Força a re-renderização dos campos para sincronizar os valores
			setTimeout(() => {
				methods.trigger("fields");
			}, 0);
		},
		[getValues, setValue, methods],
	);

	const resetForm = useCallback(() => {
		methods.reset({
			title: "",
			text: "",
			nomenclature: "",
			developerId: "",
			approverId: "",
			fields: [],
		});
	}, [methods]);

	const isFieldTypeOptions = useCallback((typeId: number) => {
		return typeId === InspectionFormTypeEnum.OPTIONS;
	}, []);

	return {
		methods,
		addField,
		removeField,
		updateField,
		reorderFields,
		resetForm,
		isFieldTypeOptions,
	};
};
